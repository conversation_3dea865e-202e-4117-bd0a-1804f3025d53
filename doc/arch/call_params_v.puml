@startsalt
{
{T
  + AgentInfo                             | Tags                   | Description
  ++ id                                  | core_api               | Unique identifier for the agent.
  ++ name                                | core_api               | Name of the agent, potentially sourced from `human_name`.
  ++ model                               | core_api               | Model type of the agent.
  ++ profile                             | core_api               | Name of the agent's profile.
  ++ voiceId                             | tts_api                | Identifier for the agent’s voice.
  ++ companyId                           | core_api               | Identifier for the company to which the agent belongs.
  ++ missionId                           | prompt                 | Identifier for the mission.
  ++ langfusePromptId                    | prompt                 | Langfuse identifier (not used currently).
  ++ voice                               | tts_api                | Reference to the Voice object.
  +++ VOICESETTINGS                      | tts_api                | Settings related to the voice.
  ++++ provider                          | tts_api                | Voice provider.
  ++++ stability                         | tts_api                | Stability of the voice output.
  ++++ similarity_boost                  | tts_api                | Level of similarity enhancement.
  ++++ style                             | tts_api                | Style of the voice.
  ++++ use_speaker_boost                 | tts_api                | Whether to use speaker boost.
  ++++ language                          | tts_api                | Language(s) used in the voice (comma-separated list).
}
{T
  + SessionOptions                       | Tags                   | Description
  ++ session_id                          | logging_api            | Unique identifier for the session (e.g., GUID).
  ++ company_id                          | core_api               | Identifier for the company involved in the session.
  ++ participant                         | core_api               | Participant identifier (e.g., "callee").
  ++ conversation_id                     | logging_api            | Identifier for tracking the conversation.
  ++ campaign_id                         | campaign_specifics     | Identifier associated with the campaign.
  ++ conversation_type                   | campaign_specifics     | Type of conversation (e.g., `test-call`, `outbound-campaign-call`).
  ++ timezone                            | core_api               | Timezone of the session (default: UTC).
  ++ agent_id                            | core_api               | Identifier of the agent participating in the session (linked to AgentInfo.id).
  ++ room_name                           | logging_api            | Name of the session room (deprecated).
}
}
@enduml
