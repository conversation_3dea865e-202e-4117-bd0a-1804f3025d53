#!/usr/bin/env bash
set -euo pipefail

# Base settings
BASE_URL="https://etisalat.infra.callevate.ai/DirServices"
SESSION_ID="123"
DDSESSION_ID="EE14FF6AE221E8A7EF3FE0B8749AD17C%3A%2FDirServices"

# 1) Start the session
echo "1) Fetching Start → app.vxml"
curl -s "${BASE_URL}/Start?session=${SESSION_ID}" -o app.vxml

# 2) Get the welcome VXML
echo "2) Fetching Welcome → welcome.vxml"
curl -s "${BASE_URL}/Welcome?___DDSESSIONID=${DDSESSION_ID}" -o welcome.vxml

# 3) Get the language‐collection prompt
echo "3) Fetching CollectLanguage_input → collect_language.vxml"
curl -s "${BASE_URL}/CollectLanguage_input?___DDSESSIONID=${DDSESSION_ID}" -o collect_language.vxml

# 4) Submit your language choice
echo "4) Posting LanguageSelection → language_selection.vxml"
curl -s -X POST "${BASE_URL}/LanguageSelection?___DDSESSIONID=${DDSESSION_ID}" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "CollectLanguage_input___value=English" \
     -d "CollectLanguage_input___confidence=0.95" \
     -d "CollectLanguage_input___utterance=English" \
     -d "CollectLanguage_input___inputmode=voice" \
     -d "CollectLanguage_input___interpretation=en-US" \
     -d "CollectLanguage_input___noinputcount=0" \
     -d "CollectLanguage_input___nomatchcount=0" \
     -d "CollectLanguage_input___LanguageCode=en-US" \
     -o language_selection.vxml

# 5) Get the scope‐collection prompt
echo "5) Fetching CollectScope_input → collect_scope.vxml"
curl -s "${BASE_URL}/CollectScope_input?___DDSESSIONID=${DDSESSION_ID}" -o collect_scope.vxml

# 6) Submit your scope choice
echo "6) Posting ScopeSelection → scope_selection.vxml"
curl -s -X POST "${BASE_URL}/ScopeSelection?___DDSESSIONID=${DDSESSION_ID}" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "CollectScope_input___value=directory services" \
     -d "CollectScope_input___confidence=0.92" \
     -d "CollectScope_input___utterance=directory services" \
     -d "CollectScope_input___inputmode=voice" \
     -d "CollectScope_input___interpretation=directory services" \
     -d "CollectScope_input___noinputcount=0" \
     -d "CollectScope_input___nomatchcount=0" \
     -d "CollectScope_input___ScopeName=directory services" \
     -o scope_selection.vxml

echo "Done. Inspect the .vxml files at each step to see the next <submit> and repeat as needed."