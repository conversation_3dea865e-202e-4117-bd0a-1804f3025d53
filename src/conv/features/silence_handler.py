import asyncio
import logging
import threading
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor
from multiprocessing import Event as ProcessEvent
from threading import Event as ThreadEvent
from typing import Callable, Optional

from livekit.agents import AgentSession, AgentStateChangedEvent, UserStateChangedEvent

from app.config import get_config


def create_event():
    try:
        return ProcessEvent()
    except ImportError:
        return ThreadEvent()


_logger = logging.getLogger(__name__)


class SilenceHandler:
    """
    Enhanced silence handler that supports both timer-based and LiveKit event-driven silence detection.
    Manages idle state, progressive silence responses, and conversation termination.
    """

    def __init__(
        self,
        say_idle_callback: Callable[[], None],
        end_conversation_callback: Callable[[], None],
        user_away_timeout: float = None,
        max_idle_attempts: int = 3,
        timer_duration: float = None,
        loop: asyncio.AbstractEventLoop = None,
    ):
        # Configuration
        _config = get_config()
        self.user_away_timeout = user_away_timeout or _config.app.silence_threshold
        self.max_idle_attempts = max_idle_attempts
        self.timer_duration = timer_duration or 10.0  # Default timer duration

        # Callbacks
        self.say_idle_callback = say_idle_callback
        self.end_conversation_callback = end_conversation_callback

        # State management
        self.idle_counter = 0
        self.is_user_active = False
        self.callee_is_idle = create_event()

        # Timer-based fallback (legacy support)
        self.timer_future = None
        self.cancel_event = threading.Event()
        self.lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.loop = loop or asyncio.get_event_loop()

        # Session reference for event handling
        self.session: Optional[AgentSession] = None

        _logger.debug(f"SilenceHandler initialized with timeout={self.user_away_timeout}s, max_attempts={self.max_idle_attempts}")

    def setup_session_events(self, session: AgentSession):
        """
        Set up LiveKit session event handlers for silence detection.
        This replaces the timer-based approach with event-driven silence handling.
        """
        self.session = session

        @session.on("agent_state_changed")
        def on_agent_state_changed(event: AgentStateChangedEvent):
            """Handle agent state changes to manage silence detection."""
            if event.old_state == "speaking" and event.new_state == "listening":
                if self.callee_is_idle.is_set():
                    # Reset user away timer when agent finishes speaking and user is idle
                    session._set_user_away_timer()
                    _logger.debug("Agent finished speaking, user away timer reset")

        @session.on("user_state_changed")
        def on_user_state_changed(event: UserStateChangedEvent):
            """
            Handle user state changes for silence detection.
            LiveKit emits this every time the user's VAD/STT state changes:
              * new_state == "speaking"  → user started speaking
              * new_state == "listening" → user stopped speaking but not yet away
              * new_state == "away"      → user and agent are both silent for user_away_timeout
            """
            if event.new_state == "speaking":
                self._handle_user_speaking()
            elif event.new_state == "away":
                self._handle_user_away()

        _logger.debug("Session event handlers registered for silence detection")

    def _handle_user_speaking(self):
        """Handle when user starts speaking - reset idle state."""
        self.idle_counter = 0
        self.is_user_active = True
        self.callee_is_idle.clear()
        _logger.debug("User started speaking - idle state reset")

    def _handle_user_away(self):
        """Handle when user has been silent for the timeout period."""
        self.idle_counter += 1
        self.is_user_active = False

        if self.idle_counter <= self.max_idle_attempts:
            self.callee_is_idle.set()
            _logger.debug(
                f"Silence #{self.idle_counter} detected after {self.user_away_timeout}s - sending idle message"
            )
            try:
                self.say_idle_callback()
            except Exception as e:
                _logger.error(f"Error calling idle message callback: {e}")
        else:
            _logger.debug(f"Maximum idle attempts ({self.max_idle_attempts}) reached - ending conversation")
            try:
                self.end_conversation_callback()
            except Exception as e:
                _logger.error(f"Error calling end conversation callback: {e}")

    def reset_idle_state(self):
        """Manually reset the idle state."""
        self.idle_counter = 0
        self.is_user_active = True
        self.callee_is_idle.clear()
        _logger.debug("Idle state manually reset")

    def get_idle_count(self) -> int:
        """Get the current idle attempt count."""
        return self.idle_counter

    def is_idle(self) -> bool:
        """Check if the user is currently considered idle."""
        return self.callee_is_idle.is_set()

    def get_max_attempts(self) -> int:
        """Get the maximum number of idle attempts before ending conversation."""
        return self.max_idle_attempts

    def set_max_attempts(self, max_attempts: int):
        """Set the maximum number of idle attempts before ending conversation."""
        self.max_idle_attempts = max_attempts
        _logger.debug(f"Maximum idle attempts set to {max_attempts}")

    def get_timeout(self) -> float:
        """Get the current user away timeout value."""
        return self.user_away_timeout

    def set_timeout(self, timeout: float):
        """Set the user away timeout value."""
        self.user_away_timeout = timeout
        _logger.debug(f"User away timeout set to {timeout}s")

    # Legacy timer-based methods (kept for backward compatibility)
    def cancel_timer(self):
        """Cancel the timer-based silence detection."""
        with self.lock:
            self._cancel_timer()

    def start_timer(self):
        """Start timer-based silence detection (legacy fallback)."""
        with self.lock:
            self._cancel_timer()
            self.cancel_event.clear()
            self.timer_future = self.executor.submit(self._wait_and_trigger_timer)
            _logger.debug('Timer started: Waiting for user to start speaking.')

    def _wait_and_trigger_timer(self):
        """Timer-based silence detection implementation."""
        is_canceled = self.cancel_event.wait(timeout=self.timer_duration)
        with self.lock:
            self.timer_future = None
        if not is_canceled:
            try:
                self.say_idle_callback()
            except Exception as e:
                _logger.error(f"Error in timer-based idle callback: {e}")

    def _cancel_timer(self):
        """Cancel the current timer."""
        if self.timer_future is not None:
            self.cancel_event.set()
            self.timer_future.cancel()
            self.timer_future = None

    def shutdown(self):
        """Shutdown the silence handler and cleanup resources."""
        with self.lock:
            self._cancel_timer()
        self.executor.shutdown(wait=True)
        _logger.debug("SilenceHandler shutdown completed")