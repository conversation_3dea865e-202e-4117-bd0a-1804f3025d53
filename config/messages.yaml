# User-facing Messages Configuration
# This file contains all user-facing messages, phrases, and text content

# Silence phrases for different languages
silence_phrases:
  en-US:
    - "Are you still there?"
    - "Just let me know when you're ready."
    - "Take your time, I'm here."
    - "No rush, I'll be here when you're ready."
    - "Feel free to ask whenever you're ready."
    - "I'm here whenever you're ready to continue."
    - "Don't worry, I'm still here."
    - "Whenever you're ready, I'm listening."
    - "I'm here if you need me."
    - "Take your time, there's no hurry."
    - "I'm ready when you are."
    - "Feel free to continue when you're ready."

  ar-EG:
    - "هل ما زلت هناك؟"
    - "أخبرني فقط عندما تكون جاهزًا."
    - "خذ وقتك، أنا هنا."
    - "لا عجلة، سأكون هنا عندما تكون جاهزًا."
    - "لا تتردد في السؤال عندما تكون مستعدًا."
    - "أنا هنا عندما تكون مستعدًا للاستمرار."
    - "لا تقلق، ما زلت هنا."
    - "عندما تكون جاهزًا، أنا أستمع."
    - "أنا هنا إذا احتجت إلي."
    - "خذ وقتك، لا عجلة."
    - "أنا جاهز عندما تكون أنت."
    - "لا تتردد في المتابعة عندما تكون مستعدًا."

# Acknowledgment phrases for turn detection
acknowledgment_phrases:
  - "ok"
  - "okay"
  - "yes"
  - "uh-huh"
  - "ask"
  - "yeah"
  - "yep"
  - "yup"
  - "right"
  - "gotcha"
  - "mhm"
  - "mm-hmm"
  - "hmm"
  - "I see"
  - "sure"
  - "roger"

# Greeting phrases for repeated greetings detection
greeting_phrases:
  - "hello"
  - "hey"
  - "hi"
  - "greetings"
  - "good morning"
  - "good afternoon"
  - "good evening"
  - "hey there"
  - "hi there"

# Language instruction templates
language_instructions:
  single_language: |
    **Language Instructions:**
    - You should respond **only** in {language_name}.

  single_language_arabic: |
    **Language Instructions:**
    - You should respond **only** in {language_name}. Use UAE accent.

  multiple_languages: |
    **Language Instructions:**
    - You should respond in the same language as the user, supporting {supported_languages}.
    - Default language is {default_language}.

# Time format templates
time_format: "Current time: {current_time}  Weekday: {weekday} Time zone: {timezone}"

# Mission prompt template
mission_intro_suffix: "Your name is {human_name}. You have to start call from clearly stating your role and goal."
