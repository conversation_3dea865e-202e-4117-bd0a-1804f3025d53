FROM python:3.12

ARG LIVEKIT_URL
ENV LIVEKIT_URL=${LIVEKIT_URL}
ARG LIVEKIT_API_KEY
ENV LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
ARG LIVEKIT_API_SECRET
ENV LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
ARG ELEVENLABS_API_KEY
ENV ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
ARG LANGFUSE_SECRET_KEY
ENV LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
ARG LANGFUSE_PUBLIC_KEY
ENV LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
ARG LANGFUSE_HOST
ENV LANGFUSE_HOST=${LANGFUSE_HOST}
ARG DEEPGRAM_API_KEY
ENV DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
ARG OPENAI_API_KEY
ENV OPENAI_API_KEY=${OPENAI_API_KEY}
ARG CORE_API_URL
ENV CORE_API_URL=${CORE_API_URL}
ARG CORE_API_LOGIN
ENV CORE_API_LOGIN=${CORE_API_LOGIN}
ARG CORE_API_PASSWORD
ENV CORE_API_PASSWORD=${CORE_API_PASSWORD}
ARG CONVERSATION_API_URL
ENV CONVERSATION_API_URL=${CONVERSATION_API_URL}
ARG CONVERSATION_API_LOGIN
ENV CONVERSATION_API_LOGIN=${CONVERSATION_API_LOGIN}
ARG CONVERSATION_API_PASSWORD
ENV CONVERSATION_API_PASSWORD=${CONVERSATION_API_PASSWORD}
ARG CALL_AGENT_ID
ENV CALL_AGENT_ID=${CALL_AGENT_ID}
ARG STT_PLUGIN
ENV STT_PLUGIN=${STT_PLUGIN}
ARG SENTRY_DSN
ENV SENTRY_DSN=${SENTRY_DSN}
ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

RUN export DEBIAN_FRONTEND=noninteractive && \
    sed -i 's/: main/: contrib main non-free non-free-firmware/' /etc/apt/sources.list.d/debian.sources && \
    apt update -y && \
    apt upgrade -y && \
    apt install -y ffmpeg netcat-openbsd git-lfs wget nano && \
    pip install --root-user-action ignore pdm && \
    apt clean -y

WORKDIR /app

COPY scripts/_/_dev.sh /app
RUN chmod +x /app/_dev.sh

COPY pdm.lock pyproject.toml .

RUN pdm install

COPY . /app
ENV PYTHONPATH=/app

RUN pdm run python /app/src/setup.py

ENTRYPOINT [ "pdm", "run", "python", "src/main.py", "start" ]
#ENTRYPOINT [ "tail", "-f", "/dev/null" ]
