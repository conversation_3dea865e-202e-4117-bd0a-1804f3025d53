# Language Mappings Configuration
# This file contains language code mappings and locale configurations

# Language codes to human-readable names
language_codes_to_names:
  eng: "English"
  arb: "Arabic"
  tha: "Thai"
  rus: "Russian"
  ukr: "Ukrainian"

# Language codes to locale identifiers
language_codes_to_locale:
  eng: "en-US"
  arb: "arb"
  tha: "th"
  rus: "ru"
  ukr: "uk"

# Default language settings
default_language: "en-US"
default_language_code: "eng"

# Supported locales list
supported_locales:
  - "en-US"
  - "arb"
  - "th"
  - "ru"
  - "uk"

# Language parsing configuration
language_parsing:
  separators: "[;,]"  # Regex pattern for splitting language strings
  fallback_language: "en-US"
